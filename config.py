"""
Configuration settings for the Voice Receptionist Agent
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    # LiveKit Configuration
    LIVEKIT_URL = os.getenv("LIVEKIT_URL", "wss://your-project.livekit.cloud")
    LIVEKIT_API_KEY = os.getenv("LIVEKIT_API_KEY")
    LIVEKIT_API_SECRET = os.getenv("LIVEKIT_API_SECRET")

    # Google Gemini Configuration
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

    # Agent Configuration
    AGENT_NAME = os.getenv("AGENT_NAME", "VoiceReceptionist")
    ROOM_NAME = os.getenv("ROOM_NAME", "receptionist-room")

    # Gemini Model Configuration
    GEMINI_MODEL = "gemini-2.5-flash-preview-native-audio-dialog"  # Correct LiveKit-compatible Gemini Live API model
    GEMINI_VOICE = "Zephyr"  # Available voices: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
    GEMINI_TEMPERATURE = 0.2

    # System Instructions for Voice Receptionist
    SYSTEM_INSTRUCTIONS = """
    You are a professional and friendly voice receptionist. Your role is to:

    1. Greet callers warmly and professionally
    2. Ask how you can help them today
    3. Listen carefully to their requests
    4. Provide helpful information or direct them appropriately
    5. Be concise but thorough in your responses
    6. Maintain a pleasant and professional tone throughout the conversation
    7. If you cannot help with something, politely explain and offer alternatives

    Keep your responses natural and conversational. You are representing a business,
    so always be courteous and helpful. Speak clearly and at a moderate pace.
    """

    @classmethod
    def validate(cls):
        """Validate that required configuration is present"""
        required_vars = [
            ("LIVEKIT_URL", cls.LIVEKIT_URL),
            ("LIVEKIT_API_KEY", cls.LIVEKIT_API_KEY),
            ("LIVEKIT_API_SECRET", cls.LIVEKIT_API_SECRET),
            ("GOOGLE_API_KEY", cls.GOOGLE_API_KEY),
        ]

        missing_vars = []
        for var_name, var_value in required_vars:
            if not var_value or var_value.startswith("your_"):
                missing_vars.append(var_name)

        if missing_vars:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}. "
                f"Please check your .env file or environment variables."
            )

        return True
