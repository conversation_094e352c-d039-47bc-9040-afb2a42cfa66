"""
Voice Receptionist Agent using LiveKit Agents and Gemini Live API
"""
import asyncio
import logging

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
)
from livekit.plugins import google
from livekit import rtc

from config import Config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def entrypoint(ctx: JobContext):
    """Main entrypoint for the agent"""
    logger.info(f"Starting Voice Receptionist Agent in room: {ctx.room.name}")

    # Connect to the room
    await ctx.connect()

    # Validate configuration
    Config.validate()
    logger.info(f"Initializing Voice Receptionist with model: {Config.GEMINI_MODEL}")
    logger.info(f"Using voice: {Config.GEMINI_VOICE}")

    # Create the agent with instructions and Gemini realtime model
    agent = Agent(
        instructions=Config.SYSTEM_INSTRUCTIONS,
        llm=google.beta.realtime.RealtimeModel(
            model=Config.GEMINI_MODEL,
            voice=Config.GEMINI_VOICE,
            temperature=Config.GEMINI_TEMPERATURE,
            api_key=Config.GOOGLE_API_KEY,
        ),
    )

    # Create and start the agent session with proper audio configuration
    session = AgentSession(
        # Configure for proper audio handling
        room_input_options=rtc.RoomInputOptions(
            auto_subscribe=True,
            # Enable audio processing
            audio=True,
        ),
    )
    await session.start(
        agent=agent,
        room=ctx.room,
    )

    logger.info("Voice Receptionist Agent is now active and ready to assist")

    # Send initial greeting AFTER the session is started
    logger.info("Sending initial greeting to user")
    await session.generate_reply(
        instructions="Greet the user warmly and professionally. Say: 'Hello! Welcome to our service. I'm your voice assistant. How can I help you today?'"
    )


if __name__ == "__main__":
    # Start the CLI with our worker
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
